import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnD<PERSON>roy } from '@angular/core';
import { ToolBar, ToolEventData, ToolEventListener, ToolState } from '@viclass/editor.core';
import { GeometryTool, GeometryToolBar, GeometryToolType, QuadToolState } from '@viclass/editor.geo';
import { BehaviorSubject } from 'rxjs';
import { TOOLBAR, TOOLTYPE } from '../injection.token';

/**
 * Component for display and edit the number of edges for CreateRegularPolygonTool
 */
@Component({
    templateUrl: './quad-2side-sod.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Quad2SideSoDComponent implements AfterViewInit, OnDestroy {
    private toolListener = Quad2SideSoDComponent.ToolListener(this);

    public sod$ = new BehaviorSubject<number>(0);

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(TOOLBAR) private toolbar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType
    ) {}

    ngAfterViewInit(): void {
        this.toolbar.registerToolListener(this.toolListener);
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolListener);
    }

    setSOD(value: number) {
        const ts = this.toolbar.toolState(this.tooltype) as QuadToolState;
        ts.drawMode = value;
        this.toolbar.update(this.tooltype, ts);
    }

    private updateInputFromToolState() {
        const ts = this.toolbar.toolState(this.tooltype) as QuadToolState;

        this.sod$.next(ts.drawMode);
    }

    private static ToolListener(
        _p: Quad2SideSoDComponent
    ): ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType> {
        return new (class
            implements ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType>
        {
            onEvent(eventData: ToolEventData<any, any>): ToolEventData<any, any> {
                if (eventData.toolType != _p.tooltype) return eventData;

                _p.updateInputFromToolState();
                _p.changeDetectorRef.markForCheck();

                return eventData;
            }
        })();
    }
}
