import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnD<PERSON>roy } from '@angular/core';
import { ToolBar, ToolEventData, ToolEventListener, ToolState } from '@viclass/editor.core';
import { GeometryTool, GeometryToolBar, GeometryToolType, TriangleToolState } from '@viclass/editor.geo';
import { BehaviorSubject } from 'rxjs';
import { TOOLBAR, TOOLTYPE } from '../injection.token';

/**
 * Component for display and edit the number of edges for CreateRegularPolygonTool
 */
@Component({
    selector: 'tb-triangle-sob',
    templateUrl: './triangle-sob.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TriangleSOBComponent implements AfterViewInit, OnDestroy {
    private toolListener = TriangleSOBComponent.ToolListener(this);

    public sob$ = new BehaviorSubject<boolean>(false);

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(TOOLBAR) private toolbar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType
    ) {}

    ngAfterViewInit(): void {
        this.toolbar.registerToolListener(this.toolListener);
        this.updateInputFromToolState();
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolListener);
    }

    setSOB(value: boolean) {
        const ts = this.toolbar.toolState(this.tooltype) as TriangleToolState;
        ts.createFromBase = value;
        this.toolbar.update(this.tooltype, ts);
    }

    get sob(): boolean {
        const ts = this.toolbar.toolState(this.tooltype) as TriangleToolState;
        return ts.createFromBase;
    }

    private updateInputFromToolState() {
        this.sob$.next(this.sob);
    }

    private static ToolListener(
        _p: TriangleSOBComponent
    ): ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType> {
        return new (class
            implements ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType>
        {
            onEvent(eventData: ToolEventData<any, any>): ToolEventData<any, any> {
                if (eventData.toolType != _p.tooltype) return eventData;
                _p.updateInputFromToolState();
                _p.changeDetectorRef.markForCheck();

                return eventData;
            }
        })();
    }
}
