// import Flatten, { vector } from '@flatten-js/core';
// import {
//     buildDocumentAwarenessCmdOption,
//     ErrorHandlerDecorator,
//     pointerTypeDyn,
//     pointerTypeMouse,
//     pointerTypePen,
// } from '@viclass/editor.core';
// import { syncRenderCommands } from '../cmd';
// import { geoDefaultHandlerFn } from '../error-handler';
// import { GeometryEditor } from '../geo.editor';
// import { GeometryToolBar } from '../geo.toolbar';
// import {
//     CommonToolState,
//     ConstructionRequest,
//     GeoElConstructionRequest,
//     GeoRenderElement,
//     RenderVertex,
// } from '../model';
// import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
// import { GeoDocCtrl } from '../objects';
// import { PotentialSelectionDelegator } from '../selectors/potential.selection.delegator';
// import { constructExec, GeometryTool } from './geo.tool';
// import { NamingElementTool } from './naming.element.tool';
// import {
//     addHistoryItemFromConstructionResponse,
//     buildPointConstruction,
//     buildPreviewVertexRenderProp,
//     getPointAndVertex,
//     handleIfPointerNotInError,
//     isDifferentCoords,
//     pickPointName,
//     requestElementNames,
// } from './tool.utils';
// import point = Flatten.point;
// import line = Flatten.line;
// import circle = Flatten.circle;

// export class CreateRhombusTool extends GeometryTool<CommonToolState> {
//     readonly toolType: GeometryToolType = 'CreateRhombusTool';

//     private points: RenderVertex[] = [];
//     private isPointerDown = false;

//     private potentialSelectionDelegator: PotentialSelectionDelegator<CreateRhombusTool> =
//         new PotentialSelectionDelegator(this);

//     protected override readonly filterElementFunc = (el: GeoRenderElement) => {
//         return el.type == 'RenderVertex' && this.points.filter(p => p.relIndex == el.relIndex).length < 1;
//     };

//     constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
//         super(editor, toolbar);
//         this.registerPointerHandling(
//             // start add point
//             { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
//             { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
//             { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

//             // confirm add point
//             { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
//             { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
//             { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },

//             // move point/line preview
//             { event: 'pointermove', pointerTypes: pointerTypeMouse, keys: ['nokey'] },
//             { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
//             { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
//         );
//     }

//     override resetState() {
//         this.points = [];
//         this.isPointerDown = false;
//         super.resetState();
//     }

//     override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
//         switch (event.nativeEvent.type) {
//             case 'pointerdown': {
//                 this.onPointerDown(event);
//                 break;
//             }
//             case 'pointerup': {
//                 this.onPointerUp(event);
//                 break;
//             }
//             case 'pointermove': {
//                 this.onPointerMove(event);
//                 break;
//             }
//             default:
//                 break;
//         }
//         return event;
//     }

//     @ErrorHandlerDecorator([geoDefaultHandlerFn])
//     private async onPointerDown(event: GeoPointerEvent) {
//         if (!this.shouldHandleClick(event)) return;
//         event.continue = false;
//         event.nativeEvent.preventDefault();

//         if (this.isPointerDown) return; // don't handle if pointer is already down
//         this.isPointerDown = true;

//         if (this.points.length == 0) {
//             await this.handleFirstPoint(event);
//         } else if (this.points.length == 1) {
//             await this.handleSecondPoint(event);
//         } else if (this.points.length == 2) {
//             await this.handleThirdPoint(event);
//         }
//     }

//     @ErrorHandlerDecorator([geoDefaultHandlerFn])
//     private async onPointerUp(event: GeoPointerEvent) {
//         if (!this.shouldHandleClick(event)) return;
//         event.continue = false;
//         event.nativeEvent.preventDefault();

//         if (!this.isPointerDown) return; // don't handle if pointer down is not set
//         this.isPointerDown = false;

//         this.potentialSelectionDelegator.clearPotential();

//         if (this.points.length == 2) {
//             this.editor.filterElementFunc = el => false;
//         }

//         if (this.points.length == 3) {
//             await this.finalizeRhombus(event);
//         }
//     }

//     @ErrorHandlerDecorator([geoDefaultHandlerFn])
//     private async handleFirstPoint(event: GeoPointerEvent) {
//         const { ctrl, vertex } = getPointAndVertex(this, event);
//         this.points[0] = vertex; // add/update first point

//         this.previewRhombus(ctrl, [vertex.coords, vertex.coords]);
//         this.started = true;
//     }

//     @ErrorHandlerDecorator([geoDefaultHandlerFn])
//     private async handleSecondPoint(event: GeoPointerEvent) {
//         const { ctrl, vertex } = getPointAndVertex(this, event);
//         if (isDifferentCoords(vertex.coords, this.points[0].coords)) {
//             this.points[1] = vertex; // add/update 2nd point when it not match the first point
//         }
//         if (this.points.length !== 2) return;

//         const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
//         const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];
//         this.previewRhombus(ctrl, [v1, v2]);
//     }

//     @ErrorHandlerDecorator([geoDefaultHandlerFn])
//     private async handleThirdPoint(event: GeoPointerEvent) {
//         const { ctrl, vertex, coords } = getPointAndVertex(this, event);
//         this.points[2] = vertex; // add/update third point

//         // Calculate rhombus points based on the third point
//         const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
//         const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];

//         const p = point(coords[0], coords[1]);
//         const p1 = point(v1[0], v1[1]);
//         const p2 = point(v2[0], v2[1]);
//         if (p.equalTo(p1) || p.equalTo(p2)) return; // can not create rhombus if 3rd point is same as 1st or 2nd point

//         const c2 = circle(p2, p1.distanceTo(p2)[0]);
//         const linep1p2 = line(p1, p2);
//         const linep2p = line(p2, p);
//         const i = c2.intersect(linep2p)[1];
//         const parallelLine2 = line(i, linep1p2.norm);
//         const parallelLine1 = line(p1, linep2p.norm);
//         const intersection = parallelLine1.intersect(parallelLine2)[0];
//         const v3 = [i.x, i.y, 0.0];
//         const v4 = [intersection.x, intersection.y, 0.0];

//         this.previewRhombus(ctrl, [v1, v2, v3, v4]);
//     }

//     @ErrorHandlerDecorator([geoDefaultHandlerFn])
//     private async finalizeRhombus(event: GeoPointerEvent) {
//         const { ctrl, pos, docGlobalId } = this.posAndCtrl(event);

//         const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
//         const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];

//         const p = point(pos.x, pos.y);
//         const p1 = point(v1[0], v1[1]);
//         const p2 = point(v2[0], v2[1]);
//         const c2 = circle(p2, p1.distanceTo(p2)[0]);
//         const linep1p2 = line(p1, p2);
//         const linep2p = line(p2, p);
//         const i = c2.intersect(linep2p)[1];
//         const parallelLine2 = line(i, linep1p2.norm);
//         const parallelLine1 = line(p1, linep2p.norm);
//         const intersection = parallelLine1.intersect(parallelLine2)[0];
//         const v3 = [i.x, i.y, 0.0];
//         const v4 = [intersection.x, intersection.y, 0.0];

//         const p4 = point(v4[0], v4[1]);
//         const v12 = vector(p1, p2);
//         const v14 = vector(p1, p4);
//         const angle = v12.angleTo(v14);

//         const vertex3: RenderVertex = {
//             relIndex: -12,
//             type: 'RenderVertex',
//             elType: 'Point',
//             renderProp: buildPreviewVertexRenderProp(),
//             name: undefined,
//             coords: v3,
//             usable: true,
//             valid: true,
//         };
//         const vertex4: RenderVertex = {
//             relIndex: -13,
//             type: 'RenderVertex',
//             elType: 'Point',
//             renderProp: buildPreviewVertexRenderProp(),
//             name: undefined,
//             coords: v4,
//             usable: true,
//             valid: true,
//         };
//         this.points.push(vertex3);
//         this.points.push(vertex4);

//         // submit construction
//         let constructionPoints: GeoElConstructionRequest[] = [];
//         const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
//         const inputPointNames = (
//             await requestElementNames(ctrl, nt, [
//                 {
//                     objName: 'Hình Thoi',
//                     originElement: this.points,
//                     pickName: pickPointName,
//                     namesToAvoid: [],
//                 },
//             ])
//         )[0];
//         if (!inputPointNames.length) {
//             this.resetState();
//             return;
//         }

//         for (let i = 0; i < this.points.length; i++) {
//             const p = this.points[i];
//             if (!p.name) {
//                 p.name = inputPointNames[i];
//                 const constructionPoint = buildPointConstruction(p.name, {
//                     x: p.coords[0],
//                     y: p.coords[1],
//                 });
//                 constructionPoints.push(constructionPoint);
//             }
//         }

//         const lineName = `${this.points[0].name}${this.points[1].name}`;
//         const pName1 = inputPointNames[2];
//         const pName2 = inputPointNames[3];
//         const rhombusName = `${lineName}${pName1}${pName2}`;

//         let constructionPolygon: GeoElConstructionRequest;
//         if (this.points.length === 4) {
//             constructionPolygon = this.buildRhombusFromPointsConstruction(this.points.map(p => p.name));
//         } else if (constructionPoints.length < 2) {
//             const lineName = `${this.points[0].name}${this.points[1].name}`;
//             constructionPolygon = this.buildRhombusFromLineSegmentConstruction(rhombusName, lineName, angle);
//         } else {
//             constructionPolygon = this.buildRhombusFromTwoPositionConstruction(rhombusName, v1, v2, angle);
//             constructionPoints = [];
//         }

//         this.resetState();

//         await ctrl.editor.awarenessFeature.useAwareness(
//             ctrl.viewport.id,
//             'Đang tạo hình thoi',
//             buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
//             async () => {
//                 const constructResponse = await constructExec(() =>
//                     this.editor.geoGateway.construct(docGlobalId, [
//                         ...constructionPoints.map(
//                             c =>
//                                 <ConstructionRequest>{
//                                     construction: c,
//                                 }
//                         ),
//                         {
//                             construction: constructionPolygon,
//                         },
//                     ])
//                 );

//                 await syncRenderCommands(constructResponse.render, ctrl);
//                 await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
//             }
//         );
//     }

//     private onPointerMove(event: GeoPointerEvent) {
//         if (this.points.length == 0) return;

//         this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
//         event.continue = false;
//         event.nativeEvent.preventDefault();
//     }

//     private pointerMoveCallback(event: GeoPointerEvent) {
//         handleIfPointerNotInError(this, () => {
//             this.processPointerMove(event);
//         });
//     }

//     @ErrorHandlerDecorator([geoDefaultHandlerFn])
//     private processPointerMove(event: GeoPointerEvent) {
//         if (this.isPointerDown) {
//             if (!this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) return;
//             // while pointer down -> handle all 3 cases
//             if (this.points.length === 1) {
//                 this.handleFirstPoint(event);
//             } else if (this.points.length === 2) {
//                 this.handleSecondPoint(event);
//             } else if (this.points.length === 3) {
//                 this.handleThirdPoint(event);
//             }
//         } else {
//             // Preview for the current state
//             if (this.points.length === 2 || this.points.length === 3) {
//                 this.handleThirdPoint(event);
//             }
//         }
//     }

//     private buildRhombusFromTwoPositionConstruction(
//         name: string,
//         pos1: number[],
//         pos2: number[],
//         angle: number
//     ): GeoElConstructionRequest {
//         const construction = new GeoElConstructionRequest('Rhombus/RhombusEC', 'Rhombus', 'FromTwoPositionAndAngle');
//         construction.name = name;
//         construction.paramSpecs = [
//             {
//                 indexInCG: 0,
//                 paramDefId: 'aValue',
//                 optional: false,
//                 tplStrLangId: 'tpl-3DPoint',
//                 params: {
//                     value: {
//                         type: 'array',
//                         values: pos1,
//                     },
//                 },
//             },
//             {
//                 indexInCG: 1,
//                 paramDefId: 'aValue',
//                 optional: false,
//                 tplStrLangId: 'tpl-3DPoint',
//                 params: {
//                     value: {
//                         type: 'array',
//                         values: pos2,
//                     },
//                 },
//             },
//             {
//                 indexInCG: 2,
//                 paramDefId: 'aValue',
//                 optional: false,
//                 tplStrLangId: 'tpl-AngleRadian',
//                 params: {
//                     value: {
//                         type: 'singleValue',
//                         value: angle,
//                     },
//                 },
//             },
//         ];

//         return construction;
//     }

//     private buildRhombusFromLineSegmentConstruction(
//         name: string,
//         lineName: string,
//         angle: number
//     ): GeoElConstructionRequest {
//         const construction = new GeoElConstructionRequest('Rhombus/RhombusEC', 'Rhombus', 'LineSegmentAndAngle');
//         construction.name = name;
//         construction.paramSpecs = [
//             {
//                 indexInCG: 0,
//                 paramDefId: 'aLineSegment',
//                 optional: false,
//                 tplStrLangId: 'tpl-FromLineSegment',
//                 params: {
//                     name: {
//                         type: 'singleValue',
//                         value: lineName,
//                     },
//                 },
//             },
//             {
//                 indexInCG: 1,
//                 paramDefId: 'aValue',
//                 optional: false,
//                 tplStrLangId: 'tpl-AngleRadian',
//                 params: {
//                     value: {
//                         type: 'singleValue',
//                         value: angle,
//                     },
//                 },
//             },
//         ];

//         return construction;
//     }

//     private buildRhombusFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
//         const construction = new GeoElConstructionRequest('Rhombus/RhombusEC', 'Rhombus', 'FromPoints');
//         construction.name = pointNames.join('');
//         construction.paramSpecs = [
//             {
//                 indexInCG: 0,
//                 paramDefId: 'aPoint',
//                 optional: false,
//                 tplStrLangId: 'tpl-Points',
//                 params: {
//                     name: {
//                         type: 'array',
//                         values: pointNames,
//                     },
//                 },
//             },
//         ];

//         return construction;
//     }

//     private previewRhombus(ctrl: GeoDocCtrl, faces: number[][]) {
//         // const polygon: PreviewPolygon = {
//         //     relIndex: -20,
//         //     name: '',
//         //     type: 'RenderPolygon',
//         //     elType: 'Rhombus',
//         //     faces: faces,
//         //     renderProp: buildPreviewPolygonRenderProp(),
//         //     usable: true,
//         //     valid: true,
//         // };
//         // syncPreviewCommands(polygon, ctrl);
//     }
// }
