import { point, Point, vector, Vector } from '@flatten-js/core';
import { MouseEventData, UIPointerEventData } from '@viclass/editor.core';
import { syncEndPreviewModeCommand } from '../cmd';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { GeoElConstructionRequest, RegularPolygonToolState, RenderPolygon, RenderVector, RenderVertex } from '../model';
import { GeoKeyboardEvent, GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pPolygon, PreviewQueue, pVertex } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import {
    nothing,
    or,
    repeat,
    RepeatSelector,
    SelectedVertex,
    then,
    ThenSelector,
    vert,
    vertex,
    vertexS,
} from '../selectors';
import { Geo<PERSON>Tool } from './geo.tool';
import { buildPreviewVertexRenderProp, getFocusDocCtrl, handleIfPointerNotInError } from './tool.utils';

/**
 * Clamp the number of edges of the regular polygon within [3, 8] and round to the nearest integer.
 */
function clampEdge(noEdge: number) {
    return Math.min(
        Math.max(Math.round(noEdge) || CreateRegularPolygonTool.EDGE_MIN, CreateRegularPolygonTool.EDGE_MIN),
        CreateRegularPolygonTool.EDGE_MAX
    );
}

/**
 * Tool for creating regular polygons. Allows users to draw a regular polygon by clicking on the canvas
 * and change the number of edges by scrolling the mouse wheel while holding Ctrl.
 */
export class CreateRegularPolygonTool extends GeometryTool<RegularPolygonToolState> {
    static EDGE_MIN = 3;
    static EDGE_MAX = 10;
    static POLYGON_PREVIEW_ID = -20;

    readonly toolType: GeometryToolType = 'CreateRegularPolygonTool';

    declare selLogic?: ThenSelector;
    pQ = new PreviewQueue();

    lastTrialEvent?: UIPointerEventData<any>;
    curDoc?: GeoDocCtrl;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        // Register mouse wheel event to change the number of edges
        this.registerMouseHandling(
            {
                event: 'mousewheel',
                button: 0,
                pressedButton: 0,
                keys: ['ctrl'],
            },
            {
                event: 'wheel',
                button: 0,
                pressedButton: 0,
                keys: ['ctrl'],
            }
        );

        this.registerKeyboardHandling(
            {
                event: 'keyup',
                keys: ['x'],
                global: false,
            },
            {
                event: 'keyup',
                keys: ['X'],
                global: false,
            },
            {
                event: 'keyup',
                keys: ['shift'],
                global: false,
            }
        );

        this.createSelLogic();
    }

    private createSelLogic() {
        this.selLogic = then(
            [
                // first select two points, the first two points can be on an edge or existing points or free points
                repeat<SelectedVertex>(vertexS(this.pQ, this.pointerHandler.cursor), {
                    count: 2,
                    onComplete: this.first2Points.bind(this),
                }),

                repeat<(RenderVertex | 'nothing')[]>( // allow the user to select existing points if the additional point match
                    or(
                        // [RenderVertex | 'nothing']
                        [
                            vertex({
                                genPreview: false,
                                cursor: this.pointerHandler.cursor,
                                cfunc: this.checkAdditionalPoints.bind(this),
                            }),
                            nothing(),
                        ],
                        {}
                    ),
                    {
                        onPartialSelection: this.additionalPoints.bind(this),
                    }
                ),
            ],
            {
                flatten: 1,
                onComplete: this.performConstruction.bind(this),
            } // unbox the repeat  level so we have an array of SelectedVertex | RenderVertex type
        );
    }

    first2Points(selector: RepeatSelector<SelectedVertex>, doc: GeoDocCtrl) {}

    additionalPoints(newSel: (RenderVertex | 'nothing')[], curSel: RenderVertex[]) {
        console.log('Additional point selected', newSel);
        if (newSel[0] == 'nothing') return false;
        else {
            return true; // for now.
        }
    }

    checkAdditionalPoints(v: RenderVertex, doc: GeoDocCtrl): boolean {
        const el: RenderPolygon = doc.rendererCtrl.previewElAt(CreateRegularPolygonTool.POLYGON_PREVIEW_ID);

        if (!el) {
            return false;
        } else {
            return true;
        }
    }

    performConstruction(selector: RepeatSelector<SelectedVertex>, ctrl: GeoDocCtrl) {
        console.log(selector.selected);
        setTimeout(() => this.resetState()); // needs async because we need to check this.selLogic.isAccepted in trySelect
    }

    /**
     * Reset the tool state to be ready to create a new polygon.
     * Clear the list of points, reset relIndex, nth, pointer down flag, click count.
     * Call resetState of the parent class.
     */
    override resetState() {
        this.selLogic.reset();
        this.lastTrialEvent = undefined;
        this.curDoc = undefined;
        super.resetState();
    }

    /**
     * Handle mouse events (mousewheel, wheel) to change the number of edges
     */
    override handleMouseEvent(event: MouseEventData<any>): MouseEventData<any> {
        switch (event.nativeEvent.type) {
            case 'mousewheel':
            case 'wheel': {
                this.onMouseWheel(event, this.curDoc);
                event.continue = false;

                break;
            }
            default:
                break;
        }
        return event;
    }

    override handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        let processed = false;
        if (event.eventType == 'keyup') {
            if (event.getKeys.includes('x')) {
                processed = true;
                this.rotateNextVert();
            }
            if (event.getKeys.includes('shift')) {
                processed = true;
                this.toolState.drawMode = ++this.toolState.drawMode % 3;
            }

            if (processed) {
                this.refreshToolState();
                event.continue = false;
                event.nativeEvent.preventDefault();
            }
        }
        return event;
    }

    refreshToolState() {
        this.toolbar.update(this.toolType, this.toolState);
        if (this.lastTrialEvent && this.curDoc) this.doTrySelection(this.lastTrialEvent, this.curDoc);
    }

    rotateNextVert() {
        this.toolState.selecting = (this.toolState.selecting + 1) % this.toolState.noEdge;
        if (this.toolState.selecting == 0) this.toolState.selecting = 1;
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private setCurrentPreview(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        this.lastTrialEvent = event;
        this.curDoc = ctrl;
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        this.setCurrentPreview(event, ctrl);
        const selected = this.selLogic.trySelect(event, ctrl);
        if (this.selLogic.isAccepted) return;
        if (selected && selected.length >= 2) {
            // render the two selected point
            const v0 = vert(selected[0] as SelectedVertex);
            const v1 = vert(selected[1] as SelectedVertex);
            const p0 = point(v0.coords[0], v0.coords[1]);
            const px = point(v1.coords[0], v1.coords[1]);

            const ts = this.toolState;

            // this is a preview vertex
            const v0Temp = v0.relIndex < 0 ? v0 : pVertex(-1000, v0.coords);
            this.pQ.add(v0Temp);
            const v1Temp = v1.relIndex < 0 ? v1 : pVertex(-1001, v1.coords);
            this.pQ.add(v1Temp);
            v0Temp.renderProp = buildPreviewVertexRenderProp();
            v1Temp.renderProp = buildPreviewVertexRenderProp();
            v0Temp.renderProp.pointColor = '#ff0000';
            v1Temp.renderProp.pointColor = '#0000ff';

            // we just draw the polygon without its vertices element, The individual element is synced by the vertex selector
            const { points } =
                ts.drawMode == 0
                    ? regPolyVertFromChord(ts.noEdge, p0, px, ts.selecting)
                    : ts.drawMode == 1
                      ? regPolyVertFromCenter(ts.noEdge, p0, px)
                      : regPolyVertFromCenterMid(ts.noEdge, p0, px);

            if (points.length >= 3) {
                this.pQ.add(
                    pPolygon(
                        ctrl,
                        CreateRegularPolygonTool.POLYGON_PREVIEW_ID,
                        points.map(p => [p.x, p.y, 0]),
                        true,
                        RenderVector
                    )
                );
            }
        }

        this.pQ.flush(ctrl);
    }

    // Handle non-UI pointer events (default)

    /**
     * Handle mouse wheel event to increase/decrease the number of edges of the regular polygon.
     * Scroll up: increase edges, scroll down: decrease edges.
     */
    private async onMouseWheel(event: MouseEventData<any>, ctrl: GeoDocCtrl) {
        const wheel = (event.nativeEvent as WheelEvent).deltaY < 0 ? 1 : -1;
        const ts = this.toolbar.toolState(this.toolType) as RegularPolygonToolState;
        ts.noEdge = clampEdge(ts.noEdge + wheel);

        if (ts.selecting >= ts.noEdge) ts.selecting = ts.selecting % ts.noEdge;
        if (ts.selecting == 0) ts.selecting = 1;

        syncEndPreviewModeCommand(ctrl);
        this.refreshToolState();
    }

    /**
     * Finalize the creation of the regular polygon based on the confirmed points and direction.
     */
    // @ErrorHandlerDecorator([geoDefaultHandlerFn])
    // private async finalizePolygon(event: GeoPointerEvent) {
    //     if (!this.isValid) {
    //         this.resetState();
    //         throw new InvalidUserInputErr('Invalid number of edges');
    //     }

    //     const { ctrl } = this.posAndCtrl(event);

    //     let constructionPoints: GeoElConstructionRequest[] = [];
    //     let constructionPolygon: GeoElConstructionRequest;

    //     // Calculate the remaining vertices of the regular polygon
    //     const points = this.calculateRemainingVertices();

    //     // Create RenderVertex objects for the remaining vertices (preview)
    //     const vertexes = points.map(
    //         coords =>
    //             <RenderVertex>{
    //                 relIndex: this.relIndex--,
    //                 type: 'RenderVertex',
    //                 renderProp: buildPreviewVertexRenderProp(),
    //                 name: undefined,
    //                 coords: coords,
    //                 usable: true,
    //                 valid: true,
    //             }
    //     );

    //     // Request input for point names (if needed)
    //     const inputPointNames = (
    //         await requestElementNames(ctrl, nt, [
    //             {
    //                 objName: 'Regular Polygon',
    //                 originElement: this.points.concat(...vertexes),
    //                 pickName: pickPointName,
    //                 namesToAvoid: [],
    //             },
    //         ])
    //     )[0];
    //     if (!inputPointNames) {
    //         this.resetState();
    //         return;
    //     }

    //     // Create construction points if the point does not have a name
    //     for (let i = 0; i < this.points.length; i++) {
    //         const p = this.points[i];
    //         if (!p.name) {
    //             p.name = inputPointNames[i];
    //             const constructionPoint = buildPointConstruction(p.name, {
    //                 x: p.coords[0],
    //                 y: p.coords[1],
    //             });
    //             constructionPoints.push(constructionPoint);
    //         }
    //     }

    //     const polygonName = inputPointNames.join('');
    //     if (constructionPoints.length < 2) {
    //         // If there are enough points, create the polygon from the line segment
    //         const lineName = this.points.map(p => p.name).join('');
    //         constructionPolygon = this.buildPolygonFromLineSegmentConstruction(
    //             polygonName,
    //             lineName,
    //             this.noEdge,
    //             this.nth
    //         );
    //     } else {
    //         // If not enough points, create the polygon from two positions
    //         constructionPolygon = this.buildPolygonFromTwoPositionConstruction(
    //             polygonName,
    //             this.points[0].coords,
    //             this.points[1].coords,
    //             this.noEdge,
    //             this.nth
    //         );
    //         constructionPoints = [];
    //     }

    //     this.resetState();

    //     // Execute the command to create the regular polygon and sync render, history
    //     await ctrl.editor.awarenessFeature.useAwareness(
    //         ctrl.viewport.id,
    //         'Creating regular polygon',
    //         buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
    //         async () => {
    //             const constructResponse = await constructExec(() =>
    //                 this.editor.geoGateway.construct(ctrl.state.globalId, [
    //                     ...constructionPoints.map(
    //                         c =>
    //                             <ConstructionRequest>{
    //                                 construction: c,
    //                             }
    //                     ),
    //                     {
    //                         construction: constructionPolygon,
    //                     },
    //                 ])
    //             );

    //             await syncRenderCommands(constructResponse.render, ctrl);
    //             await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
    //         }
    //     );
    // }

    /**
     * Calculate the remaining vertices of the regular polygon based on the first two points.
     * Returns an array of coordinates of the remaining vertices.
     */
    // private calculateRemainingVertices(): number[][] {
    //     if (this.points.length < 2) return [];

    //     const points: number[][] = [];
    //     const v1 = this.points[0].coords;
    //     const v2 = this.points[1].coords;

    //     const p1 = point(v1[0], v1[1]);
    //     const p2 = point(v2[0], v2[1]);
    //     const angle = (2 * Math.PI) / this.noEdge;
    //     const size = p1.distanceTo(p2)[0];
    //     const r = size / 2 / Math.sin(angle / 2);
    //     const c1 = circle(p1, r);
    //     const c2 = circle(p2, r);
    //     const c1_x_c2 = c1.intersect(c2);

    //     if (c1_x_c2.length === 0) return []; // No intersection, cannot create polygon

    //     const pC = c1_x_c2[this.nth == 1 ? 1 : 0];

    //     // Function to create a new point by rotating around center pC
    //     const buildPoint2 = (p0: Point, i: number): number[] => {
    //         const p = p0.rotate(angle * i, pC);
    //         return [p.x, p.y, 0.0];
    //     };

    //     for (let i = 1; i < this.noEdge - 1; i += 1) {
    //         points.push(buildPoint2(p2, this.nth == 1 ? i : -i));
    //     }

    //     return points;
    // }

    /**
     * Create a construction request for a regular polygon from two positions (two points).
     */
    private buildPolygonFromTwoPositionConstruction(
        polygonName: string,
        pos1: number[],
        pos2: number[],
        noEdge: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RegularPolygon/RegularPolygonEC',
            'RegularPolygon',
            'FromTwoPosition'
        );
        construction.name = polygonName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-3DPoint',
                params: {
                    value: {
                        type: 'array',
                        values: pos1,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-3DPoint',
                params: {
                    value: {
                        type: 'array',
                        values: pos2,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-NoEdge',
                params: {
                    value: {
                        type: 'singleValue',
                        value: noEdge,
                    },
                },
            },
            {
                indexInCG: 3,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    /**
     * Create a construction request for a regular polygon from a line segment (two named points).
     */
    private buildPolygonFromLineSegmentConstruction(
        polygonName: string,
        lineName: string,
        noEdge: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RegularPolygon/RegularPolygonEC',
            'RegularPolygon',
            'FromLineSegment'
        );
        construction.name = polygonName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aName',
                optional: false,
                tplStrLangId: 'tpl-FromLineSegment',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-NoEdge',
                params: {
                    value: {
                        type: 'singleValue',
                        value: noEdge,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    /**
     * Create a construction request for a regular polygon from multiple points (not used in main flow).
     */
    private buildPolygonFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RegularPolygon/RegularPolygonEC',
            'RegularPolygon',
            'FromPoints'
        );
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
        ];

        return construction;
    }
}

function regPolyVertFromCenter(n: number, pc: Point, p0: Point): { points: Point[] } {
    if (n < 3) {
        throw new Error('Polygon must have at least 3 vertices.');
    }

    const angleStep = -(2 * Math.PI) / n; // Negative for clockwise

    const baseVector = vector(pc, p0);
    const vertices: Point[] = [];

    for (let i = 0; i < n; i++) {
        const rotated = baseVector.rotate(angleStep * i);
        const pt = pc.translate(rotated);
        vertices.push(pt);
    }

    return { points: vertices };
}

export function regPolyVertFromCenterMid(n: number, pc: Point, pm: Point): { points: Point[] } {
    if (n < 3) {
        throw new Error('Polygon must have at least 3 vertices.');
    }

    const angleStep = -(2 * Math.PI) / n; // Clockwise

    // Vector from center to midpoint of an edge
    const midVector = vector(pc, pm);
    const halfInteriorAngle = Math.PI / n;

    // Compute full radius (from center to vertex)
    const radius = midVector.length / Math.cos(halfInteriorAngle);

    // Rotate midVector by -π/n to get the direction to first vertex (clockwise)
    const toVertexVector = midVector.normalize().multiply(radius).rotate(-halfInteriorAngle);

    // First vertex
    const p0 = pc.translate(toVertexVector);

    // Generate all vertices in clockwise order
    const vertices: Point[] = [];
    for (let i = 0; i < n; i++) {
        const angle = angleStep * i;
        const rotated = toVertexVector.rotate(angle);
        const pt = pc.translate(rotated);
        vertices.push(pt);
    }

    return { points: vertices };
}

/**
 * Generates vertices of a regular polygon with:
 * - `p0` at index 0
 * - `px` at index `xth`
 * - Total of `n` vertices in either 'cw' or 'ccw' direction
 */
function regPolyVertFromChord(
    n: number,
    p0: Point,
    px: Point,
    xth: number
): { points: Point[]; center: Point; perp: Vector } {
    if (p0.distanceTo(px)[0] < 0.00001) return { points: [], center: p0, perp: vector(0, 0) }; // don't generate if too small

    if (n < 3 || xth < 1 || xth >= n) {
        throw new Error('Invalid input: n must be ≥ 3 and 1 ≤ xth < n');
    }

    const angleStep = (2 * Math.PI) / n;
    const signedAngleStep = -angleStep;
    const angleBetween = signedAngleStep * xth;

    const chordVector = vector(p0, px);
    const chordLength = chordVector.length;
    const angleHalf = angleBetween / 2;

    const dir = Math.abs(angleHalf) > Math.PI / 2 ? -1 : 1;
    const radius = chordLength / (2 * Math.sin(Math.abs(angleHalf)));

    // Midpoint of chord
    const mid = p0.translate(chordVector.multiply(0.5));

    // Perpendicular vector to find center
    const perp = chordVector.rotate(-Math.PI / 2).multiply(dir); // when the angle between p0 and px > 180, the center of the polygon flip
    const distToCenter = Math.sqrt(radius * radius - (chordLength / 2) ** 2);
    const center = mid.translate(perp.normalize().multiply(distToCenter));

    // Vector from center to p0
    const baseVector = vector(center, p0);

    // Generate vertices
    const vertices: Point[] = [p0];
    for (let i = 1; i < n; i++) {
        const angle = signedAngleStep * i;
        const rotated = baseVector.rotate(angle);
        const pt = center.translate(rotated);
        vertices.push(pt);
    }

    // // Reorder so that p0 is at index 0
    // const indexOfP0 = vertices.findIndex(pt => pt.equalTo(p0));
    // const reordered: Point[] = [];
    // for (let i = 0; i < n; i++) {
    //     const idx = (indexOfP0 + i) % n;
    //     reordered.push(vertices[idx]);
    // }

    // Ensure px is at index xth
    // if (!reordered[xth].equalTo(px)) {
    //     throw new Error('Could not place px at xth index. Check for precision issues.');
    // }

    return { points: vertices, center, perp };
}
