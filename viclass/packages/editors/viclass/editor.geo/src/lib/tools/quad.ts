/**
 * Quadilateral related tools
 */

import { Point, point, vector } from '@flatten-js/core';
import { <PERSON>rrorHandlerDecorator, UIPointerEventData } from '@viclass/editor.core';
import { syncRemovePreviewCmd } from '../cmd';
import { geoDefaultHandlerFn, GeometryEditor, GeoPointerEvent, GeoPointerNotInError, RenderVertex } from '../geo.api';
import { GeometryToolBar } from '../geo.toolbar';
import { GeoElConstructionRequest, QuadToolState, RenderLineSegment } from '../model';
import { GeoKeyboardEvent, GeometryToolType } from '../model/geo.models';
import { pLine, pPolygon, PreviewQueue, pVertex } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import {
    circleCheck,
    circleTransform,
    halfCircleCheck,
    halfCircleTransform,
    nPoints,
    perpBisector<PERSON>heck,
    perpBisectorTransform,
    perp<PERSON>ines<PERSON>heck,
    perpLinesTransform,
    pointsFromSelected,
    SelectedVertex,
    then,
    ThenSelector,
    triangleWithProj,
    vert,
    vertexS,
} from '../selectors';
import { isocelesTriangleBaseNApex, rightTriangleBaseNApex } from './create.triangle.tool';
import { GeometryTool } from './geo.tool';
import { getFocusDocCtrl } from './tool.utils';

abstract class QuadToolBase extends GeometryTool<QuadToolState> {
    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    numOptions = 2;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
        this.registerKeyboardHandling({
            event: 'keyup',
            keys: ['shift'],
            global: false,
        });
    }

    abstract createSelLogic();

    override resetState() {
        this.selLogic.reset();
        super.resetState();
    }

    override handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        console.log(this.numOptions);
        if (event.eventType == 'keyup' && event.getKeys.includes('shift')) {
            this.toolState.drawMode = ++this.toolState.drawMode % this.numOptions;
            this.toolbar.update(this.toolType, this.toolState);
        }

        return event;
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event => this.doTrySelection(event, ctrl));
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    abstract doTrySelection(event: UIPointerEventData<any>, doc: GeoDocCtrl);
}

export class CreateRectangleTool extends QuadToolBase {
    readonly toolType: GeometryToolType = 'CreateRectangleTool';

    createSelLogic() {
        this.selLogic = triangleWithProj(
            this.pQ,
            this.pointerHandler.cursor,
            (f2p, el) => (this.toolState.drawMode == 0 ? perpLinesTransform(f2p, el) : halfCircleTransform(f2p, el)),
            (f2p, el) => (this.toolState.drawMode == 0 ? perpLinesCheck(f2p, el) : halfCircleCheck(f2p, el)),
            {
                onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
            }
        );
    }

    doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        try {
            const selected = this.selLogic.trySelect(event, ctrl);

            if (selected) {
                if (selected.length == 2) {
                    // has all data, preview triangle
                    const rv = [...(selected[0] as SelectedVertex[]), selected[1] as RenderVertex].map(v => vert(v));

                    const [apex, b1, b2] = rightTriangleBaseNApex(rv, this.toolState.drawMode == 1);

                    this.pQ.add(pLine(ctrl, -51, RenderLineSegment, rv[apex], rv[b1]));
                    this.pQ.add(pLine(ctrl, -52, RenderLineSegment, rv[apex], rv[b2]));

                    const c = point(
                        (rv[b1].coords[0] + rv[b2].coords[0]) / 2,
                        (rv[b1].coords[1] + rv[b2].coords[1]) / 2
                    );
                    const x = c.translate(vector(c.x - rv[apex].coords[0], c.y - rv[apex].coords[1]));
                    const p4 = pVertex(-21, [x.x, x.y, 0]);

                    this.pQ.add(pLine(ctrl, -53, RenderLineSegment, p4, rv[b1]));
                    this.pQ.add(pLine(ctrl, -54, RenderLineSegment, p4, rv[b2]));

                    if (ctrl.rendererCtrl.previewElAt(-20)) syncRemovePreviewCmd(-20, ctrl);
                } else if (selected.length == 1 && Array.isArray(selected[0]) && selected[0].length == 2) {
                    // preview the base
                    this.pQ.add(
                        pLine(
                            ctrl,
                            -20,
                            RenderLineSegment,
                            vert(selected[0][0] as SelectedVertex),
                            vert(selected[0][1] as SelectedVertex)
                        )
                    );
                }
            }
            this.pQ.flush(ctrl);
        } catch (e) {
            console.log('Some error occur', e);
        }
    }

    performConstruction(selector: ThenSelector, doc: GeoDocCtrl) {}

    private buildRectangleFromLineSegmentAndLengthConstruction(
        rectangleName: string,
        lineName: string,
        length: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Rectangle/RectangleEC', 'Rectangle', 'LineSegmentAndLength');
        construction.name = rectangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-FromLineSegment',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-LengthValue',
                params: {
                    value: {
                        type: 'singleValue',
                        value: length,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private buildRectangleFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Rectangle/RectangleEC', 'Rectangle', 'FromPoints');
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
        ];

        return construction;
    }
}

export class CreateParallelogramTool extends QuadToolBase {
    readonly toolType: GeometryToolType = 'CreateParallelogramTool';
    override numOptions = 3;

    createSelLogic() {
        const lastVertex = vertexS(this.pQ, this.pointerHandler.cursor);
        this.selLogic = then([nPoints(this.pQ, this.pointerHandler.cursor, { count: 2 }), lastVertex], {
            onComplete: this.performConstruction.bind(this),
        });
    }

    findClosest(el: RenderVertex, doc: GeoDocCtrl): { ops: Point[]; minIdx: number } {
        const s = [...(this.selLogic.selected[0] as SelectedVertex[])];

        const ps = pointsFromSelected(s);
        const pointerP = point(el.coords[0], el.coords[1]);
        // for each point, we calculate the oposite point through the middle of the other two points
        const ops = ps.map((p, i) => {
            const j = (i + 1) % 3,
                k = (j + 1) % 3;
            return p.translate(vector(ps[i], ps[j]).add(vector(ps[i], ps[k])));
        });
        let minDist = 100000,
            minIdx = -1;
        for (let i = 0; i < 3; i++) {
            const d = ops[i].distanceTo(pointerP)[0];
            if (minDist > d) {
                minDist = d;
                minIdx = i;
            }
        }

        return { ops, minIdx };
    }

    doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        try {
            const selected = this.selLogic.trySelect(event, ctrl);

            if (selected) {
                if (selected.length == 2) {
                    // has all data, preview triangle
                    const ps = pointsFromSelected([
                        ...(selected[0] as SelectedVertex[]),
                        selected[1] as SelectedVertex,
                    ]);

                    const ts = this.toolState;
                    let p4: Point;

                    const pp =
                        ts.drawMode == 0
                            ? [ps[0], ps[1], ps[2].translate(vector(ps[0], ps[1])), ps[2]] // draw by side
                            : ts.drawMode == 1
                              ? [ps[0], ps[1], ps[2], ps[2].translate(vector(ps[1], ps[0]))] // draw by side 1
                              : [ps[0], ps[2], ps[1], ps[2].translate(vector(ps[2], ps[0]).add(vector(ps[2], ps[1])))]; // draw by diagonal

                    this.pQ.add(
                        pPolygon(
                            ctrl,
                            -21,
                            pp.map(p => [p.x, p.y, 0]),
                            true
                        )
                    );

                    if (ctrl.rendererCtrl.previewElAt(-20)) syncRemovePreviewCmd(-20, ctrl);
                } else if (selected.length == 1 && Array.isArray(selected[0]) && selected[0].length == 2) {
                    // preview the base
                    this.pQ.add(
                        pLine(
                            ctrl,
                            -20,
                            RenderLineSegment,
                            vert(selected[0][0] as SelectedVertex),
                            vert(selected[0][1] as SelectedVertex)
                        )
                    );
                }
            }
            this.pQ.flush(ctrl);
        } catch (e) {
            console.log('Some error occur', e);
        }
    }

    performConstruction(selector: ThenSelector, doc: GeoDocCtrl) {}

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async finalizeParallelogram(event: GeoPointerEvent) {
        // const { ctrl, docGlobalId } = this.posAndCtrl(event);
        // // Calculate the fourth point
        // const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        // const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];
        // const v3 = [this.points[2].coords[0], this.points[2].coords[1], 0];
        // const p = point(v3[0], v3[1]);
        // const p1 = point(v1[0], v1[1]);
        // const p2 = point(v2[0], v2[1]);
        // const linep1p2 = line(p1, p2);
        // const linep2p = line(p2, p);
        // const parallelLine2 = line(p, linep1p2.norm);
        // const parallelLine1 = line(p1, linep2p.norm);
        // const intersection = parallelLine1.intersect(parallelLine2)[0];
        // const v4 = [intersection.x, intersection.y, 0.0];
        // const vertex4: RenderVertex = {
        //     relIndex: -21,
        //     type: 'RenderVertex',
        //     elType: 'Point',
        //     renderProp: buildPreviewVertexRenderProp(),
        //     name: undefined,
        //     coords: v4,
        //     usable: true,
        //     valid: true,
        // };
        // // Submit construction
        // const constructionPoints: GeoElConstructionRequest[] = [];
        // const inputPointNames = (
        //     await requestElementNames(ctrl, nt, [
        //         {
        //             objName: 'Hình Bình Hành',
        //             originElement: this.points.concat(vertex4),
        //             pickName: pickPointName,
        //             namesToAvoid: [],
        //         },
        //     ])
        // )[0];
        // if (!inputPointNames.length) {
        //     this.resetState();
        //     return;
        // }
        // for (let i = 0; i < this.points.length; i++) {
        //     const p = this.points[i];
        //     if (!p.name) {
        //         p.name = inputPointNames[i];
        //         const constructionPoint = buildPointConstruction(p.name, {
        //             x: p.coords[0],
        //             y: p.coords[1],
        //         });
        //         constructionPoints.push(constructionPoint);
        //     }
        // }
        // const lineName = `${this.points[0].name}${this.points[1].name}`;
        // const parallelogramName = `${lineName}${inputPointNames[2]}${inputPointNames[3]}`;
        // let constructionParallelogram: GeoElConstructionRequest;
        // if (this.points.length == 3) {
        //     constructionParallelogram = this.buildParallelogramConstruction(parallelogramName);
        // } else if (this.points.length == 4) {
        //     constructionParallelogram = this.buildParallelogramFromPointsConstruction(this.points.map(p => p.name));
        // }
        // this.resetState();
        // await ctrl.editor.awarenessFeature.useAwareness(
        //     ctrl.viewport.id,
        //     'Đang tạo hình bình hành',
        //     buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
        //     async () => {
        //         const constructResponse = await constructExec(() =>
        //             this.editor.geoGateway.construct(docGlobalId, [
        //                 ...constructionPoints.map(
        //                     c =>
        //                         <ConstructionRequest>{
        //                             construction: c,
        //                         }
        //                 ),
        //                 {
        //                     construction: constructionParallelogram,
        //                 },
        //             ])
        //         );
        //         await syncRenderCommands(constructResponse.render, ctrl);
        //         await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
        //     }
        // );
    }

    private buildParallelogramConstruction(name: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'Parallelogram/ParallelogramEC',
            'Parallelogram',
            'ByPointsName'
        );
        construction.name = name;
        construction.paramSpecs = [];

        return construction;
    }

    private buildParallelogramFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'Parallelogram/ParallelogramEC',
            'Parallelogram',
            'FromPoints'
        );
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
        ];

        return construction;
    }

    private async previewParallelogram(ctrl: GeoDocCtrl, ...faces: number[][]) {
        // const polygon: PreviewPolygon = {
        //     relIndex: -20,
        //     name: '',
        //     type: 'RenderPolygon',
        //     elType: 'Parallelogram',
        //     faces: faces,
        //     renderProp: buildPreviewPolygonRenderProp(),
        //     usable: true,
        //     valid: true,
        // };
        // await syncPreviewCommands(polygon, ctrl);
    }
}

export class CreateRhombusTool extends QuadToolBase {
    readonly toolType: GeometryToolType = 'CreateRhombusTool';
    override numOptions = 3;

    createSelLogic() {
        this.selLogic = triangleWithProj(
            this.pQ,
            this.pointerHandler.cursor,
            (f2p, el) =>
                this.toolState.drawMode == 2
                    ? perpBisectorTransform(f2p, el)
                    : this.toolState.drawMode == 0
                      ? circleTransform(f2p, el, 0)
                      : circleTransform(f2p, el, 1),
            (f2p, el) => (this.toolState.drawMode == 2 ? perpBisectorCheck(f2p, el) : circleCheck(f2p, el)),
            {
                onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
            }
        );
    }

    doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        try {
            const selected = this.selLogic.trySelect(event, ctrl);

            if (selected) {
                if (selected.length == 2) {
                    // has all data, preview triangle
                    const rv = [...(selected[0] as SelectedVertex[]), selected[1] as RenderVertex].map(v => vert(v));

                    const [apex, b1, b2] = isocelesTriangleBaseNApex(rv, this.toolState.drawMode == 2);

                    this.pQ.add(pLine(ctrl, -51, RenderLineSegment, rv[apex], rv[b1]));
                    this.pQ.add(pLine(ctrl, -52, RenderLineSegment, rv[apex], rv[b2]));

                    const c = point(
                        (rv[b1].coords[0] + rv[b2].coords[0]) / 2,
                        (rv[b1].coords[1] + rv[b2].coords[1]) / 2
                    );
                    const x = c.translate(vector(c.x - rv[apex].coords[0], c.y - rv[apex].coords[1]));
                    const p4 = pVertex(-21, [x.x, x.y, 0]);

                    this.pQ.add(pLine(ctrl, -53, RenderLineSegment, p4, rv[b1]));
                    this.pQ.add(pLine(ctrl, -54, RenderLineSegment, p4, rv[b2]));

                    if (ctrl.rendererCtrl.previewElAt(-20)) syncRemovePreviewCmd(-20, ctrl);
                } else if (selected.length == 1 && Array.isArray(selected[0]) && selected[0].length == 2) {
                    // preview the base
                    this.pQ.add(
                        pLine(
                            ctrl,
                            -20,
                            RenderLineSegment,
                            vert(selected[0][0] as SelectedVertex),
                            vert(selected[0][1] as SelectedVertex)
                        )
                    );
                }
            }
            this.pQ.flush(ctrl);
        } catch (e) {
            console.log('Some error occur', e);
        }
    }

    performConstruction(selector: ThenSelector, doc: GeoDocCtrl) {}

    private buildRectangleFromLineSegmentAndLengthConstruction(
        rectangleName: string,
        lineName: string,
        length: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Rectangle/RectangleEC', 'Rectangle', 'LineSegmentAndLength');
        construction.name = rectangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-FromLineSegment',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-LengthValue',
                params: {
                    value: {
                        type: 'singleValue',
                        value: length,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private buildRectangleFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Rectangle/RectangleEC', 'Rectangle', 'FromPoints');
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
        ];

        return construction;
    }
}
