import { buildDocumentAwarenessCmdOption } from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, RenderAngle, RenderLine, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { constructExec, GeometryTool } from './geo.tool';
import {
    addHistoryItemFromConstructionResponse,
    calculateScalingFactor,
    calculateUnitVector,
    handleIfPointerNotInError,
    isElementLine,
    pickPointName,
    requestElementNames,
} from './tool.utils';
import { NamingElementTool } from './naming.element.tool';

/**
 * Bisector Line Tool - Creates bisector lines using selector pattern
 * <AUTHOR>
 */
export class CreateBisectorLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateBisectorLineTool';

    private selectedAngle: RenderAngle | undefined;
    private selectedEndPoint: RenderVertex | undefined;
    private selectedIntersectLine: RenderLine | undefined;
    private bisectorVector: number[] | undefined;

    protected override readonly filterElementFunc = (el: any) => {
        return el.type === 'RenderAngle' && !this.selectedAngle;
    };

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
    }

    override resetState() {
        this.selectedAngle = undefined;
        this.selectedEndPoint = undefined;
        this.selectedIntersectLine = undefined;
        this.bisectorVector = undefined;
        super.resetState();
    }

    /**
     * Calculates the bisector vector for the selected angle
     */
    private calculateBisectorVector() {
        if (!this.selectedAngle) return;

        this.bisectorVector = this.calculateBiSectorVectorOfAngle(
            this.selectedAngle.vectorStart,
            this.selectedAngle.vectorEnd
        );
    }

    private calculateBiSectorVectorOfAngle(a: number[], b: number[]): number[] {
        // Calculate the dot product of a and b
        const dotProduct = a[0] * b[0] + a[1] * b[1];

        // Magnitude of vector a
        const magnitudeA = Math.sqrt(a[0] * a[0] + a[1] * a[1]);

        // Magnitude of vector b
        const magnitudeB = Math.sqrt(b[0] * b[0] + b[1] * b[1]);

        // Calculate the cosine of the angle between a and b
        const cosTheta = dotProduct / (magnitudeA * magnitudeB);

        // Calculate the sine of the angle between a and b
        const sinTheta = Math.sqrt(1 - cosTheta * cosTheta);

        // Calculate the x and y components of the angle bisector vector
        const cX = (magnitudeA * b[1] - magnitudeB * a[1]) / (2 * sinTheta);
        const cY = (magnitudeB * a[0] - magnitudeA * b[0]) / (2 * sinTheta);

        return [cX, cY];
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.selectedAngle) {
            await this.onSelectAngle(event);
        } else {
            await this.onConstructBisector(event);
        }
    }

    private onPointerMove(event: GeoPointerEvent) {
        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(_event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            // Handle pointer move if needed
        });
    }

    private async onSelectAngle(event: GeoPointerEvent) {
        const { ctrl, hitCtx, hitEl } = this.posAndCtrl(event);
        if (hitEl && hitEl.type === 'RenderAngle') {
            this.selectedAngle = hitEl as RenderAngle;
            this.calculateBisectorVector();
            ctrl.editor.selectElement(hitCtx, true);
        }
    }

    private async onConstructBisector(event: GeoPointerEvent) {
        const { ctrl, hitEl } = this.posAndCtrl(event);

        // Check if user clicked on a point or line for additional construction options
        if (hitEl) {
            if (hitEl.type === 'RenderVertex') {
                this.selectedEndPoint = hitEl as RenderVertex;
            } else if (isElementLine(hitEl)) {
                this.selectedIntersectLine = hitEl as RenderLine;
            }
        }

        await this.performConstruction(ctrl);
    }

    /**
     * Performs the bisector line construction based on selected elements
     */
    private async performConstruction(ctrl: GeoDocCtrl) {
        if (!this.selectedAngle || !this.bisectorVector) return;

        const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
        const angleName = this.selectedAngle.name;
        let constructionRequest: GeoElConstructionRequest;

        if (!this.selectedEndPoint && !this.selectedIntersectLine) {
            // Simple bisector line
            constructionRequest = this.buildBisectorConstruction(angleName);
        } else if (this.selectedEndPoint && !this.selectedIntersectLine) {
            // Bisector segment to a point
            const inputPointNames = (
                await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Bisector Point',
                        originElement: [this.selectedEndPoint],
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ])
            )[0];

            if (!inputPointNames.length) {
                this.resetState();
                return;
            }

            const anglePoint = ctrl.rendererCtrl.elementAt(this.selectedAngle.anglePointIdx) as RenderVertex;
            const pointStart = anglePoint.coords;
            const pointEnd = this.selectedEndPoint.coords;
            const uVector = calculateUnitVector(this.bisectorVector);
            const k = calculateScalingFactor(uVector, pointStart, pointEnd);

            constructionRequest = this.buildBisectorSegmentConstruction(inputPointNames.join(''), angleName, k);
        } else if (!this.selectedEndPoint && this.selectedIntersectLine) {
            // Bisector segment with intersection line
            const intersectLineName = this.selectedIntersectLine.name;
            const intersectLineType = this.selectedIntersectLine.elType;
            constructionRequest = this.buildBisectorSegmentAndIntersectionLineConstruction(
                angleName,
                intersectLineName,
                intersectLineType
            );
        } else {
            // Default to simple bisector
            constructionRequest = this.buildBisectorConstruction(angleName);
        }

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Đang tạo đường phân giác',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(ctrl.state.globalId, [
                        {
                            construction: constructionRequest,
                        },
                    ])
                );

                await syncRenderCommands(constructResponse.render, ctrl);
                addHistoryItemFromConstructionResponse(ctrl, constructResponse);
                this.resetState();
            }
        );
    }

    private buildBisectorConstruction(angleName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('LineVi/BisectorOfAngleEC', 'LineVi', 'BisectorAngle');

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildBisectorSegmentConstruction(name: string, angleName: string, k: number): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('LineVi/BisectorOfAngleEC', 'LineVi', 'BisectorAngleSegment');
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'singleValue',
                        value: k,
                    },
                },
            },
        ];

        return construction;
    }

    private buildBisectorSegmentAndIntersectionLineConstruction(
        angleName: string,
        intersectionLineName: string,
        intersectionLineType: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/BisectorOfAngleEC',
            'LineVi',
            'BisectorAngleSegmentWithIntersectionLine'
        );

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'anAngle',
                optional: false,
                tplStrLangId: 'tpl-BisectorOfAngle',
                params: {
                    name: {
                        type: 'singleValue',
                        value: angleName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-IntersectionLine',
                params: {
                    name: {
                        type: 'singleValue',
                        value: intersectionLineName,
                    },
                },
                dataTypes: {
                    name: intersectionLineType,
                },
            },
        ];

        return construction;
    }
}
