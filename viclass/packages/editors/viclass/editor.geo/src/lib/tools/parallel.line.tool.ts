import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, RenderLine, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { stroke, then, ThenSelector, vertex } from '../selectors';
import { GeometryTool } from './geo.tool';
import { onFinalClick, previewPointerMove } from './parallel_perpendicular.line.tool.utils';
import { getFocusDocCtrl, handleIfPointerNotInError } from './tool.utils';

/**
 * Parallel Line Tool - Creates parallel lines using selector pattern
 * <AUTHOR>
 */
export class CreateParallelLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateParallelLineTool';

    declare selLogic: ThenSelector;
    private pQ = new PreviewQueue();
    private selectedLine: RenderLine | undefined;
    private selectedPoint: RenderVertex | undefined;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        this.selLogic?.reset();
        this.selectedLine = undefined;
        this.selectedPoint = undefined;
        super.resetState();
    }

    /**
     * Creates the selection logic: select line first, then point, then show preview
     */
    private createSelLogic() {
        // First select a line
        const lineSelector = stroke({
            selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Then select a point
        const pointSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Main selection logic: select line, then point, then show preview
        this.selLogic = then([lineSelector, pointSelector], {
            onComplete: (selector: ThenSelector, doc: GeoDocCtrl) => {
                const selected = selector.selected;
                if (!selected || selected.length < 2) return;

                this.selectedLine = selected[0] as RenderLine;
                this.selectedPoint = selected[1] as RenderVertex;

                // Show preview when both are selected
                this.showPreview(doc);
            },
        });
    }

    /**
     * Shows the parallel line preview
     */
    private showPreview(_doc: GeoDocCtrl) {
        if (!this.selectedLine || !this.selectedPoint) return;

        // Show preview using existing utility
        previewPointerMove(
            this,
            null,
            {
                point: this.selectedPoint,
                line: this.selectedLine,
            },
            false
        ); // false for parallel (not perpendicular)
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove') {
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        } else {
            this.doTrySelection(event, ctrl);
        }

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: any, ctrl: GeoDocCtrl) {
        // If both elements are selected, handle final construction click
        if (this.selectedLine && this.selectedPoint && event.eventType === 'pointerup') {
            onFinalClick(
                this,
                event,
                {
                    point: this.selectedPoint,
                    line: this.selectedLine,
                    lastHitCtx: this.lastHitCtx,
                    buildLineSegmentWithIntersectLine: this.buildLineSegmentWithIntersectLine,
                    buildLineSegment: this.buildLineSegment,
                    buildLine: this.buildLine,
                },
                false // false for parallel (not perpendicular)
            );
            return;
        }

        // Otherwise, continue with selection logic
        this.selLogic.trySelect(event, ctrl);
        this.pQ.flush(ctrl);
    }

    private buildLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/ParallelWithOtherEC',
            'LineVi',
            'ThroughPointParallelWithLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-ParallelWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildLineSegment(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string,
        k: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/ParallelWithOtherEC',
            'LineVi',
            'ThroughPointSegmentParallelWithLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-ParallelWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'singleValue',
                        value: k,
                    },
                },
            },
        ];

        return construction;
    }

    private buildLineSegmentWithIntersectLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        intersectionLineName: string,
        intersectionLineType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/ParallelWithOtherEC',
            'LineVi',
            'ThroughPointSegmentParallelWithLineAndIntersectionLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-ParallelWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-IntersectionLine',
                params: {
                    name: {
                        type: 'singleValue',
                        value: intersectionLineName,
                    },
                },
                dataTypes: {
                    name: intersectionLineType,
                },
            },
        ];

        return construction;
    }
}
